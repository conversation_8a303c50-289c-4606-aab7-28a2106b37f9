{"name": "investra-email-api-server", "version": "1.0.0", "description": "TypeScript API server for Investra email processing", "main": "dist/standalone-enhanced-server.js", "scripts": {"build": "tsc", "start": "node dist/standalone-enhanced-server.js", "start:simple": "node dist/simple-production-server.js", "start:enhanced": "node dist/standalone-enhanced-server.js", "start:dev": "node dist/server-ts.js", "dev": "ts-node standalone-enhanced-server.ts", "dev:simple": "ts-node production-server.ts", "dev:mock": "ts-node server-ts.ts", "dev:watch": "nodemon --exec ts-node production-server.ts", "start:js": "node server.js", "test:connection": "ts-node -e \"import('./production-server')\""}, "keywords": ["email", "processing", "api", "investra"], "author": "Investra AI", "license": "ISC", "dependencies": {"@types/jsonwebtoken": "^9.0.10", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "imapflow": "^1.0.188", "jsonwebtoken": "^9.0.2", "node-fetch": "^3.3.2", "winston": "^3.17.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/node": "^20.19.1", "@types/winston": "^2.4.4", "nodemon": "^3.0.2", "ts-node": "^10.9.0", "typescript": "^5.3.0"}}