name: Deploy Investra AI to RHEL VM

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: self-hosted

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: |
          set -e
          npm ci

      - name: Build application
        run: |
          set -e
          # Copy production environment variables
          cp .env.production .env
          npm run build:prod

      - name: Pre-deployment system check
        run: |
          echo "🔍 System Information:"
          echo "OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d= -f2 | tr -d '\"')"
          echo "User: $USER"
          echo "Current directory: $(pwd)"
          echo "Available space: $(df -h . | tail -1 | awk '{print $4}')"
          
          echo "🔍 Service Status:"
          if systemctl is-active --quiet nginx; then
            echo "✅ Nginx is running"
          else
            echo "⚠️  Nginx is not running"
          fi
          
          if systemctl is-active --quiet firewalld; then
            echo "🔥 Firewalld is active"
          else
            echo "ℹ️  Firewalld is not active"
          fi
          
          if command -v getenforce &> /dev/null; then
            echo "🔒 SELinux status: $(getenforce)"
          else
            echo "ℹ️  SELinux is not available"
          fi

      - name: Setup web server
        run: |
          # Install Nginx if not present
          if ! command -v nginx &> /dev/null; then
            echo "🔧 Installing Nginx..."
            if command -v dnf &> /dev/null; then
              sudo dnf install -y nginx
            elif command -v yum &> /dev/null; then
              sudo yum install -y nginx
            else
              echo "❌ Package manager not found. Please install Nginx manually."
              exit 1
            fi
          else
            echo "✅ Nginx is already installed"
          fi

          # Stop nginx first to clean up any existing configs
          sudo systemctl stop nginx

          # Backup and replace main nginx.conf to eliminate conflicts
          sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup
          
          # Create a clean nginx.conf without default server blocks
          sudo tee /etc/nginx/nginx.conf > /dev/null << 'EOF'
          user nginx;
          worker_processes auto;
          error_log /var/log/nginx/error.log;
          pid /run/nginx.pid;
          
          include /usr/share/nginx/modules/*.conf;
          
          events {
              worker_connections 1024;
          }
          
          http {
              log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                              '$status $body_bytes_sent "$http_referer" '
                              '"$http_user_agent" "$http_x_forwarded_for"';
          
              access_log /var/log/nginx/access.log main;
          
              sendfile on;
              tcp_nopush on;
              tcp_nodelay on;
              keepalive_timeout 65;
              types_hash_max_size 4096;
          
              include /etc/nginx/mime.types;
              default_type application/octet-stream;
          
              include /etc/nginx/conf.d/*.conf;
          }
          EOF

          # Remove any default configurations
          sudo rm -f /etc/nginx/conf.d/default.conf
          sudo rm -rf /etc/nginx/sites-enabled/
          sudo rm -rf /etc/nginx/sites-available/

          # Create conf.d directory
          sudo mkdir -p /etc/nginx/conf.d

          # Start nginx
          sudo systemctl enable nginx
          sudo systemctl start nginx

          # Configure firewall for HTTP traffic (if firewalld is active)
          if systemctl is-active --quiet firewalld; then
            echo "🔥 Configuring firewall for HTTP traffic..."
            sudo firewall-cmd --permanent --add-service=http
            sudo firewall-cmd --reload
          fi

          # Check if SELinux is enabled and configure if necessary
          if command -v getenforce &> /dev/null && [ "$(getenforce)" != "Disabled" ]; then
            echo "🔒 Configuring SELinux for web server..."
            sudo setsebool -P httpd_can_network_connect 1
            sudo semanage fcontext -a -t httpd_exec_t "/var/www/investra-ai(/.*)?" 2>/dev/null || true
            sudo restorecon -R /var/www/investra-ai 2>/dev/null || true
          fi

      - name: Prepare deployment directories
        run: |
          echo "🔍 Checking build output..."
          if [ ! -d "dist" ]; then
            echo "❌ dist directory not found!"
            exit 1
          fi
          
          echo "📁 Build output contents:"
          ls -la dist/
          
          if [ ! -f "dist/index.html" ]; then
            echo "❌ index.html not found in dist directory!"
            exit 1
          fi
          
          echo "✅ Build output verified"
          
          sudo mkdir -p /var/www/investra-ai
          sudo chown $USER:$USER /var/www/investra-ai
          mkdir -p /tmp/investra-ai-deploy
          cp -r dist/* /tmp/investra-ai-deploy/
          
          echo "📁 Deployment staging contents:"
          ls -la /tmp/investra-ai-deploy/

      - name: Finalize deployment
        run: |
          # Detect web server user automatically
          WEB_USER=""
          if id nginx >/dev/null 2>&1; then
            WEB_USER="nginx"
          elif id www-data >/dev/null 2>&1; then
            WEB_USER="www-data"
          elif id apache >/dev/null 2>&1; then
            WEB_USER="apache"
          elif id httpd >/dev/null 2>&1; then
            WEB_USER="httpd"
          else
            echo "⚠️  No standard web server user found, using current user: $USER"
            WEB_USER="$USER"
          fi

          echo "🔍 Using web server user: $WEB_USER"

          # Backup current deployment if it exists
          if [ -d "/var/www/investra-ai/current" ]; then
            sudo mv /var/www/investra-ai/current /var/www/investra-ai/backup-$(date +%Y%m%d-%H%M%S)
          fi

          # Move new deployment to final location
          sudo mv /tmp/investra-ai-deploy /var/www/investra-ai/current
          sudo chown -R "$WEB_USER:$WEB_USER" /var/www/investra-ai/current
          sudo chmod -R 755 /var/www/investra-ai/current

          # Create/update Nginx configuration
          sudo tee /etc/nginx/conf.d/investra-ai.conf > /dev/null << 'EOF'
          server {
              listen 80 default_server;
              listen [::]:80 default_server;
              server_name _;

              root /var/www/investra-ai/current;
              index index.html;

              # Enable compression
              gzip on;
              gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

              # Security headers
              add_header X-Frame-Options "SAMEORIGIN" always;
              add_header X-Content-Type-Options "nosniff" always;
              add_header X-XSS-Protection "1; mode=block" always;
              add_header Referrer-Policy "strict-origin-when-cross-origin" always;

              # Health check endpoint - must come before catch-all location
              location = /health {
                  access_log off;
                  return 200 "healthy\n";
                  add_header Content-Type text/plain;
              }

              # Cache static assets
              location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                  expires 1y;
                  add_header Cache-Control "public, immutable";
                  access_log off;
              }

              # Handle React Router (SPA routing) - this catches everything else
              location / {
                  try_files $uri $uri/ /index.html;
              }

              # Error pages
              error_page 404 /index.html;
              error_page 500 502 503 504 /index.html;

              # Detailed logging for debugging
              access_log /var/log/nginx/investra-ai-access.log;
              error_log /var/log/nginx/investra-ai-error.log;
          }
          EOF

          # Test Nginx configuration
          sudo nginx -t

          # Restart Nginx completely to ensure clean state
          if [ $? -eq 0 ]; then
              echo "✅ Nginx configuration is valid, restarting..."
              sudo systemctl restart nginx
              sleep 3
              echo "✅ Deployment successful! Nginx restarted."
          else
              echo "❌ Nginx configuration test failed!"
              exit 1
          fi

          # Clean up old backups (keep last 5)
          sudo find /var/www/investra-ai -name "backup-*" -type d | sort -r | tail -n +6 | xargs sudo rm -rf

          # Display deployment info
          echo "🚀 Investra AI deployed successfully!"
          echo "📁 Deployed to: /var/www/investra-ai/current"
          echo "🌐 Available at: http://$(hostname -I | awk '{print $1}')"
          echo "📊 Build size: $(du -sh /var/www/investra-ai/current)"

      - name: Verify deployment
        run: |
          # Wait a moment for Nginx to fully reload
          sleep 5
          
          # Get the server IP
          SERVER_IP=$(hostname -I | awk '{print $1}')
          
          echo "🔍 Debug information:"
          echo "Server IP: $SERVER_IP"
          echo "Nginx configuration test:"
          sudo nginx -t
          
          echo "🔍 Active Nginx configurations:"
          sudo ls -la /etc/nginx/conf.d/
          
          echo "🔍 Application files:"
          ls -la /var/www/investra-ai/current/ | head -10
          
          echo "🔍 Testing Nginx response directly:"
          sudo systemctl status nginx --no-pager -l
          
          # Test health endpoint with more verbose output
          echo "🔍 Testing health endpoint..."
          HEALTH_RESPONSE=$(curl -v -f -s "http://$SERVER_IP/health" 2>&1 || echo "FAILED")
          if echo "$HEALTH_RESPONSE" | grep -q "healthy"; then
            echo "✅ Health check passed"
            echo "Response: $HEALTH_RESPONSE"
          else
            echo "⚠️  Health check failed"
            echo "Response: $HEALTH_RESPONSE"
            echo "📋 Detailed Nginx error logs:"
            sudo tail -20 /var/log/nginx/error.log
            echo "📋 Investra AI specific error logs:"
            sudo tail -10 /var/log/nginx/investra-ai-error.log 2>/dev/null || echo "No specific error log found"
          fi
          
          # Test main application with more verbose output
          echo "🔍 Testing main application..."
          APP_RESPONSE=$(curl -v -f -s "http://$SERVER_IP/" 2>&1 || echo "FAILED")
          if echo "$APP_RESPONSE" | grep -qi "investra\|html\|<!DOCTYPE"; then
            echo "✅ Application is responding correctly"
          else
            echo "❌ Application test failed!"
            echo "Response preview: $(echo "$APP_RESPONSE" | head -5)"
            echo "📋 Nginx status:"
            sudo systemctl status nginx --no-pager -l
            echo "📋 Nginx access logs:"
            sudo tail -10 /var/log/nginx/investra-ai-access.log 2>/dev/null || sudo tail -10 /var/log/nginx/access.log
            echo "📋 Nginx error logs:"
            sudo tail -20 /var/log/nginx/error.log
            echo "📋 Directory permissions:"
            sudo ls -la /var/www/investra-ai/current/
            echo "📋 File ownership:"
            sudo ls -la /var/www/investra-ai/current/index.html 2>/dev/null || echo "index.html not found"
            exit 1
          fi
          
          echo "🎉 Deployment verification completed successfully!"
